from tortoise.models import Model
from tortoise import fields
from datetime import datetime
from .recording import Recording
import uuid


class Appointment(Model):
    """Customer appointments"""

    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    appointment_type = fields.CharField(max_length=10)
    appointment_date = fields.DatetimeField()
    amount = fields.DecimalField(max_digits=10, decimal_places=2)
    notes = fields.TextField(null=True)
    status = fields.CharField(max_length=20, default="scheduled")
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)

    # Foreign key relations
    customer = fields.ForeignKeyField(
        "models.Customer", related_name="appointments", on_delete=fields.CASCADE
    )
    package = fields.ForeignKeyField(
        "models.Package",
        related_name="appointments",
        null=True,
        on_delete=fields.CASCADE,
    )
    service = fields.ForeignKeyField(
        "models.Service",
        related_name="appointments",
        null=True,
        on_delete=fields.CASCADE,
    )
    staff = fields.ForeignKeyField(
        "models.Staff", related_name="appointments", on_delete=fields.CASCADE
    )

    # Reverse relations
    recordings: fields.ReverseRelation["Recording"]

    class Meta:
        table = "appointments"

    @classmethod
    async def check_staff_availability(
        cls, staff_id: uuid.UUID, start_time: datetime, end_time: datetime
    ) -> bool:
        """Check if staff is available - Prisma-like"""
        conflicting = await cls.filter(
            staff_id=staff_id,
            status__in=["scheduled", "confirmed", "in_progress"],
            start_time__lt=end_time,
            end_time__gt=start_time,
        ).exists()
        return not conflicting

    def __str__(self):
        return f"(Appointment_id={self.id}, date={self.appointment_date.date()}, time={self.appointment_date.time()}, status={self.status} )"

    @classmethod
    async def get_prev_appointments(cls, customer_id: str):
        appointments = (
            await Appointment.filter(customer_id=customer_id)
            .prefetch_related("recordings", "staff", "service", "package", "customer")
            .limit(5)
        )

        return appointments

    @classmethod
    async def get_appointment_staff(cls, appointment_id: str):
        appointments = (
            await Appointment.filter(id=appointment_id)
            .prefetch_related("staff")
            .first()
        )

        return appointments

    @classmethod
    async def create_appointment(cls, **data) -> "Appointment":
        """Create new appointment with validation"""
        # Validate staff availability
        # if not await cls.check_staff_availability(
        #     data["staff_id"], data["start_time"], data["end_time"]
        # ):
        #     raise ValueError("Staff not available at requested time")
        return await cls.create(**data)

    # @classmethod
    # async def get_prev_appointments()
